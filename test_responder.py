import imaplib
import smtplib
import email
from datetime import datetime

def test_email_check():
    """Test email checking functionality"""
    EMAIL = "<EMAIL>"
    PASSWORD = "isreboakabzjwkpu"
    
    print(f"🚀 Testing email responder at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📧 Email: {EMAIL}")
    
    try:
        # Connect to IMAP
        print("\n1. Connecting to IMAP...")
        imap = imaplib.IMAP4_SSL("imap.mail.yahoo.com", 993)
        imap.login(EMAIL, PASSWORD)
        print("✓ IMAP connected successfully")
        
        # Select inbox
        print("\n2. Selecting inbox...")
        imap.select('INBOX')
        print("✓ Inbox selected")
        
        # Search for unread emails
        print("\n3. Searching for unread emails...")
        status, messages = imap.search(None, 'UNSEEN')
        
        if status != 'OK':
            print("✗ Error searching for emails")
            return
        
        email_ids = messages[0].split()
        print(f"✓ Found {len(email_ids)} unread emails")
        
        # Process each email
        for i, email_id in enumerate(email_ids[:3]):  # Limit to first 3 emails
            print(f"\n4.{i+1} Processing email {email_id.decode()}...")
            
            try:
                status, msg_data = imap.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    print(f"✗ Failed to fetch email {email_id}")
                    continue
                
                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])
                        
                        # Extract details
                        from_email = email.utils.parseaddr(msg['From'])[1]
                        from_name = email.utils.parseaddr(msg['From'])[0] or from_email.split('@')[0]
                        subject = msg['Subject'] or "No Subject"
                        
                        print(f"   📧 From: {from_name} ({from_email})")
                        print(f"   📝 Subject: {subject}")
                        
                        # Check if it's worth responding to
                        if from_email.lower() == EMAIL.lower():
                            print("   ⏭️ Skipping (self email)")
                            continue
                        
                        if any(keyword in from_email.lower() for keyword in ['noreply', 'no-reply', 'automated']):
                            print("   ⏭️ Skipping (automated email)")
                            continue
                        
                        print("   ✅ This email would get a response!")
                        
            except Exception as e:
                print(f"   ✗ Error processing email {email_id}: {e}")
        
        # Close IMAP
        imap.close()
        imap.logout()
        print("\n✓ IMAP connection closed")
        
        # Test SMTP
        print("\n5. Testing SMTP connection...")
        smtp = smtplib.SMTP("smtp.mail.yahoo.com", 587)
        smtp.starttls()
        smtp.login(EMAIL, PASSWORD)
        print("✓ SMTP connected successfully")
        smtp.quit()
        print("✓ SMTP connection closed")
        
        print("\n🎉 All tests passed! Email responder should work.")
        
    except Exception as e:
        print(f"\n✗ Error: {e}")

if __name__ == "__main__":
    test_email_check()
