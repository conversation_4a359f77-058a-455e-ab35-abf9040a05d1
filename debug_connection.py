import imaplib
import smtplib
import ssl

def debug_yahoo_connection(email, password):
    """Debug Yahoo connection with detailed error information"""
    
    print(f"Debugging connection for: {email}")
    print("=" * 50)
    
    # Test 1: Basic IMAP connection
    print("\n1. Testing basic IMAP connection...")
    try:
        context = ssl.create_default_context()
        imap = imaplib.IMAP4_SSL("imap.mail.yahoo.com", 993, ssl_context=context)
        print("✓ SSL connection established")
        
        # Try login
        result = imap.login(email, password)
        print(f"✓ Login successful: {result}")
        
        # Test selecting inbox
        imap.select('INBOX')
        print("✓ Inbox selected successfully")
        
        imap.logout()
        print("✓ IMAP test completed successfully")
        
    except imaplib.IMAP4.error as e:
        print(f"✗ IMAP error: {e}")
        print("This usually means authentication failed.")
        print("Solutions:")
        print("- Generate an app-specific password from Yahoo")
        print("- Enable 2-factor authentication first")
        print("- Check if account is locked")
        
    except Exception as e:
        print(f"✗ Connection error: {e}")
    
    # Test 2: SMTP connection
    print("\n2. Testing SMTP connection...")
    try:
        smtp = smtplib.SMTP("smtp.mail.yahoo.com", 587)
        smtp.starttls()
        print("✓ SMTP TLS connection established")
        
        smtp.login(email, password)
        print("✓ SMTP login successful")
        
        smtp.quit()
        print("✓ SMTP test completed successfully")
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"✗ SMTP authentication error: {e}")
        print("Solutions:")
        print("- Use app-specific password")
        print("- Enable less secure app access")
        
    except Exception as e:
        print(f"✗ SMTP error: {e}")
    
    # Test 3: Check Yahoo security settings
    print("\n3. Yahoo Security Checklist:")
    print("□ Go to: https://login.yahoo.com/account/security")
    print("□ Enable 2-Factor Authentication")
    print("□ Generate App Password for 'Mail'")
    print("□ Use the 16-character app password instead of regular password")
    print("□ Alternative: Enable 'Less secure app access' (if available)")

if __name__ == "__main__":
    EMAIL = "<EMAIL>"
    PASSWORD = "Zied74198462342"
    
    debug_yahoo_connection(EMAIL, PASSWORD)
