import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from datetime import datetime
import time
import sqlite3

class SimpleAIResponder:
    def __init__(self, email_address, password):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.mail.yahoo.com"
        self.smtp_server = "smtp.mail.yahoo.com"
        self.imap_port = 993
        self.smtp_port = 587
        
        # Database to track which emails we've already responded to
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database to track responded emails"""
        try:
            self.conn = sqlite3.connect('responded_emails.db')
            self.cursor = self.conn.cursor()
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS responded_emails (
                    message_id TEXT PRIMARY KEY,
                    responded_at TIMESTAMP,
                    original_subject TEXT,
                    sender_email TEXT,
                    ai_response TEXT
                )
            ''')
            self.conn.commit()
            print("✓ Database initialized")
        except Exception as e:
            print(f"Database error: {e}")
    
    def connect_imap(self):
        """Connect to Yahoo IMAP server"""
        try:
            print("Connecting to IMAP...")
            self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.imap.login(self.email_address, self.password)
            print("✓ IMAP connected successfully")
            return True
        except Exception as e:
            print(f"✗ IMAP connection error: {e}")
            return False
    
    def connect_smtp(self):
        """Connect to Yahoo SMTP server"""
        try:
            print("Connecting to SMTP...")
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()
            self.smtp.login(self.email_address, self.password)
            print("✓ SMTP connected successfully")
            return True
        except Exception as e:
            print(f"✗ SMTP connection error: {e}")
            return False
    
    def extract_email_content(self, msg):
        """Extract text content from email message"""
        try:
            content = ""
            
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                if msg.get_content_type() == "text/plain":
                    content = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
            
            return content.strip()
        except Exception as e:
            print(f"Error extracting content: {e}")
            return ""
    
    def generate_smart_response(self, sender_name, subject, email_content):
        """Generate smart response based on keywords"""
        try:
            content_lower = email_content.lower()
            subject_lower = subject.lower()
            
            # Determine response type based on content
            if any(word in content_lower + subject_lower for word in ['meeting', 'schedule', 'appointment', 'calendar']):
                specific_response = "I'll check my calendar and get back to you about scheduling. Please let me know your preferred time slots and I'll do my best to accommodate."
                
            elif any(word in content_lower + subject_lower for word in ['question', 'help', 'support', 'assistance', 'how to']):
                specific_response = "I'll look into your question and provide you with the information you need. Thank you for reaching out for assistance."
                
            elif any(word in content_lower + subject_lower for word in ['urgent', 'asap', 'immediately', 'emergency', 'important']):
                specific_response = "I understand this is urgent and will prioritize your request. I'll get back to you as soon as possible."
                
            elif any(word in content_lower + subject_lower for word in ['thank', 'thanks', 'appreciate']):
                specific_response = "Thank you for your kind message. I appreciate you taking the time to reach out."
                
            elif any(word in content_lower + subject_lower for word in ['proposal', 'business', 'collaboration', 'partnership']):
                specific_response = "Thank you for your business inquiry. I'll review your proposal carefully and respond with detailed information."
                
            elif any(word in content_lower + subject_lower for word in ['invoice', 'payment', 'bill', 'account']):
                specific_response = "I've received your message regarding billing/payment. I'll review the details and get back to you promptly."
                
            else:
                specific_response = "I'll review your message carefully and respond accordingly."
            
            # Create personalized response
            response_body = f"""Dear {sender_name},

Thank you for your email regarding "{subject}".

{specific_response}

I will get back to you as soon as possible with a detailed response.

Best regards,
Isa Chott Mariem

---
This intelligent auto-response was generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            return response_body
            
        except Exception as e:
            print(f"Error generating response: {e}")
            return f"Dear {sender_name},\n\nThank you for your email. I will get back to you soon.\n\nBest regards,\nIsa Chott Mariem"
    
    def check_if_responded(self, message_id):
        """Check if we've already responded to this email"""
        try:
            self.cursor.execute(
                "SELECT message_id FROM responded_emails WHERE message_id = ?", 
                (message_id,)
            )
            return self.cursor.fetchone() is not None
        except Exception as e:
            print(f"Database check error: {e}")
            return False
    
    def mark_as_responded(self, message_id, subject, sender_email, ai_response):
        """Mark email as responded in database"""
        try:
            self.cursor.execute(
                "INSERT INTO responded_emails (message_id, responded_at, original_subject, sender_email, ai_response) VALUES (?, ?, ?, ?, ?)",
                (message_id, datetime.now(), subject, sender_email, ai_response)
            )
            self.conn.commit()
        except Exception as e:
            print(f"Database save error: {e}")
    
    def get_unread_emails(self):
        """Fetch unread emails from inbox"""
        try:
            print("Selecting inbox...")
            self.imap.select('INBOX')
            
            print("Searching for unread emails...")
            status, messages = self.imap.search(None, 'UNSEEN')
            
            if status != 'OK':
                print("Error searching for emails")
                return []
            
            email_ids = messages[0].split()
            print(f"Found {len(email_ids)} unread emails")

            # Limit to most recent 10 emails to avoid overwhelming
            recent_email_ids = email_ids[-10:] if len(email_ids) > 10 else email_ids
            print(f"Processing most recent {len(recent_email_ids)} emails...")

            emails = []

            for email_id in recent_email_ids:
                try:
                    status, msg_data = self.imap.fetch(email_id, '(RFC822)')
                    
                    if status != 'OK':
                        continue
                    
                    for response_part in msg_data:
                        if isinstance(response_part, tuple):
                            msg = email.message_from_bytes(response_part[1])
                            emails.append((email_id, msg))
                except Exception as e:
                    print(f"Error processing email {email_id}: {e}")
                    continue
            
            return emails
        
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def send_response(self, to_email, original_subject, response_text):
        """Send response email"""
        try:
            print(f"Sending response to {to_email}...")
            
            msg = MIMEMultipart()
            msg['From'] = self.email_address
            msg['To'] = to_email
            msg['Subject'] = f"Re: {original_subject}"
            
            msg.attach(MIMEText(response_text, 'plain'))
            
            self.smtp.send_message(msg)
            print(f"✓ Response sent successfully to {to_email}")
            return True
            
        except Exception as e:
            print(f"✗ Error sending response: {e}")
            return False
    
    def process_emails(self):
        """Main process to check and respond to emails"""
        print("\n" + "="*50)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Starting email check...")
        
        try:
            # Connect to servers
            if not self.connect_imap():
                print("Failed to connect to IMAP")
                return
            
            if not self.connect_smtp():
                print("Failed to connect to SMTP")
                return
            
            # Get unread emails
            unread_emails = self.get_unread_emails()

            if not unread_emails:
                print("✅ No unread emails found - inbox is clean!")
                return
            
            # Process each email
            for email_id, msg in unread_emails:
                try:
                    # Extract email details
                    message_id = msg.get('Message-ID', '')
                    from_email = email.utils.parseaddr(msg['From'])[1]
                    from_name = email.utils.parseaddr(msg['From'])[0] or from_email.split('@')[0]
                    subject = msg['Subject'] or "No Subject"
                    email_content = self.extract_email_content(msg)
                    
                    print(f"\n📧 Processing: {from_email} - {subject}")
                    
                    # Skip if already responded
                    if self.check_if_responded(message_id):
                        print("⏭️ Already responded - skipping")
                        continue

                    # Skip self emails
                    if from_email.lower() == self.email_address.lower():
                        print("⏭️ Self email - skipping")
                        continue

                    # Skip automated emails
                    if any(keyword in from_email.lower() for keyword in ['noreply', 'no-reply', 'automated']):
                        print("⏭️ Automated email - skipping")
                        continue
                    
                    # Generate and send response
                    print("🤖 Generating smart response...")
                    response_text = self.generate_smart_response(from_name, subject, email_content)
                    
                    if self.send_response(from_email, subject, response_text):
                        self.mark_as_responded(message_id, subject, from_email, response_text)
                        
                        # Mark as read
                        self.imap.store(email_id, '+FLAGS', '\\Seen')
                        print("✅ Email processed successfully!")
                    
                except Exception as e:
                    print(f"Error processing individual email: {e}")
                    continue
            
        except Exception as e:
            print(f"Error in main process: {e}")
        
        finally:
            self.disconnect()
    
    def disconnect(self):
        """Close connections"""
        try:
            if hasattr(self, 'imap'):
                self.imap.close()
                self.imap.logout()
            if hasattr(self, 'smtp'):
                self.smtp.quit()
            print("✓ Connections closed")
        except Exception as e:
            print(f"Disconnect error: {e}")
    
    def run_continuously(self, check_interval=300):
        """Run the responder continuously"""
        print(f"🚀 Starting Simple AI Email Responder")
        print(f"📧 Email: {self.email_address}")
        print(f"⏰ Check interval: {check_interval} seconds")
        
        while True:
            try:
                self.process_emails()
                print(f"\n💤 Waiting {check_interval} seconds until next check...")
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\n🛑 Stopping responder...")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")
                print("Waiting 60 seconds before retry...")
                time.sleep(60)

# Usage
if __name__ == "__main__":
    EMAIL = "<EMAIL>"
    PASSWORD = "isreboakabzjwkpu"
    
    responder = SimpleAIResponder(EMAIL, PASSWORD)
    responder.run_continuously(check_interval=300)
