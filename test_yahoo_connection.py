import imaplib
import smtplib

def test_yahoo_connection(email, password):
    """Test Yahoo email connection with given credentials"""
    
    print(f"Testing connection for: {email}")
    
    # Test IMAP connection
    print("\n1. Testing IMAP connection...")
    try:
        imap = imaplib.IMAP4_SSL("imap.mail.yahoo.com", 993)
        imap.login(email, password)
        print("✓ IMAP connection successful!")
        imap.logout()
    except Exception as e:
        print(f"✗ IMAP connection failed: {e}")
        return False
    
    # Test SMTP connection
    print("\n2. Testing SMTP connection...")
    try:
        smtp = smtplib.SMTP("smtp.mail.yahoo.com", 587)
        smtp.starttls()
        smtp.login(email, password)
        print("✓ SMTP connection successful!")
        smtp.quit()
    except Exception as e:
        print(f"✗ SMTP connection failed: {e}")
        return False
    
    print("\n✓ All connections successful! Your credentials work.")
    return True

if __name__ == "__main__":
    # Test with your credentials
    EMAIL = "<EMAIL>"
    PASSWORD = "Zied74198462342"  # Replace with app password
    
    test_yahoo_connection(EMAIL, PASSWORD)
