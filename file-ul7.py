import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from datetime import datetime
import time
import sqlite3
import os

class YahooAutoResponder:
    def __init__(self, email_address, password):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.mail.yahoo.com"
        self.smtp_server = "smtp.mail.yahoo.com"
        self.imap_port = 993
        self.smtp_port = 587
        
        # Database to track which emails we've already responded to
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database to track responded emails"""
        self.conn = sqlite3.connect('responded_emails.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS responded_emails (
                message_id TEXT PRIMARY KEY,
                responded_at TIMESTAMP
            )
        ''')
        self.conn.commit()
    
    def connect_imap(self):
        """Connect to Yahoo IMAP server"""
        try:
            self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.imap.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"IMAP connection error: {e}")
            return False
    
    def connect_smtp(self):
        """Connect to Yahoo SMTP server"""
        try:
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()
            self.smtp.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def check_if_responded(self, message_id):
        """Check if we've already responded to this email"""
        self.cursor.execute(
            "SELECT message_id FROM responded_emails WHERE message_id = ?", 
            (message_id,)
        )
        return self.cursor.fetchone() is not None
    
    def mark_as_responded(self, message_id):
        """Mark email as responded in database"""
        self.cursor.execute(
            "INSERT INTO responded_emails (message_id, responded_at) VALUES (?, ?)",
            (message_id, datetime.now())
        )
        self.conn.commit()
    
    def get_unread_emails(self):
        """Fetch unread emails from inbox"""
        try:
            self.imap.select('INBOX')
            status, messages = self.imap.search(None, 'UNSEEN')
            
            if status != 'OK':
                print("Error searching for emails")
                return []
            
            email_ids = messages[0].split()
            emails = []
            
            for email_id in email_ids:
                status, msg_data = self.imap.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    continue
                
                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])
                        emails.append((email_id, msg))
            
            return emails
        
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def create_auto_response(self, original_subject, sender_name, email_content=""):
        """Create intelligent auto-response message"""
        # Simple AI-like logic based on keywords
        content_lower = email_content.lower()
        subject_lower = original_subject.lower()

        # Determine response type based on content
        if any(word in content_lower + subject_lower for word in ['meeting', 'schedule', 'appointment', 'calendar']):
            specific_response = "I'll check my calendar and get back to you about scheduling. Please let me know your preferred time slots."

        elif any(word in content_lower + subject_lower for word in ['question', 'help', 'support', 'assistance', 'how to']):
            specific_response = "I'll look into your question and provide you with the information you need. Thank you for reaching out."

        elif any(word in content_lower + subject_lower for word in ['urgent', 'asap', 'immediately', 'emergency', 'important']):
            specific_response = "I understand this is urgent and will prioritize your request. I'll get back to you as soon as possible."

        elif any(word in content_lower + subject_lower for word in ['thank', 'thanks', 'appreciate']):
            specific_response = "Thank you for your kind message. I appreciate you taking the time to reach out."

        elif any(word in content_lower + subject_lower for word in ['proposal', 'business', 'collaboration', 'partnership']):
            specific_response = "Thank you for your business inquiry. I'll review your proposal carefully and respond with details."

        elif any(word in content_lower + subject_lower for word in ['invoice', 'payment', 'bill', 'account']):
            specific_response = "I've received your message regarding billing/payment. I'll review the details and get back to you promptly."

        else:
            specific_response = "I'll review your message carefully and respond accordingly."

        # Create personalized response
        response_body = f"""Dear {sender_name},

Thank you for your email regarding "{original_subject}".

{specific_response}

I will get back to you as soon as possible with a detailed response.

Best regards,
Isa Chott Mariem

---
This intelligent auto-response was generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        return response_body
    
    def extract_email_content(self, msg):
        """Extract text content from email message"""
        content = ""

        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            if msg.get_content_type() == "text/plain":
                content = msg.get_payload(decode=True).decode('utf-8', errors='ignore')

        return content.strip()

    def send_auto_response(self, to_email, original_subject, sender_name, email_content=""):
        """Send intelligent auto-response email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_address
            msg['To'] = to_email
            msg['Subject'] = f"Re: {original_subject}"

            body = self.create_auto_response(original_subject, sender_name, email_content)
            msg.attach(MIMEText(body, 'plain'))

            self.smtp.send_message(msg)
            print(f"Intelligent auto-response sent to {to_email}")
            return True

        except Exception as e:
            print(f"Error sending auto-response: {e}")
            return False
    
    def process_emails(self):
        """Main process to check and respond to emails"""
        if not self.connect_imap() or not self.connect_smtp():
            print("Failed to connect to email servers")
            return
        
        try:
            unread_emails = self.get_unread_emails()
            print(f"Found {len(unread_emails)} unread emails")
            
            for email_id, msg in unread_emails:
                # Extract email details
                message_id = msg.get('Message-ID', '')
                from_email = email.utils.parseaddr(msg['From'])[1]
                from_name = email.utils.parseaddr(msg['From'])[0] or from_email.split('@')[0]
                subject = msg['Subject'] or "No Subject"
                email_content = self.extract_email_content(msg)

                print(f"\nProcessing email from {from_email}: {subject}")

                # Skip if we've already responded
                if self.check_if_responded(message_id):
                    print(f"Already responded to email from {from_email}")
                    continue

                # Skip if it's from yourself (avoid loops)
                if from_email.lower() == self.email_address.lower():
                    continue

                # Skip if it appears to be an automated email
                if any(keyword in from_email.lower() for keyword in ['noreply', 'no-reply', 'automated']):
                    print(f"Skipping automated email from {from_email}")
                    continue

                # Send intelligent auto-response
                print("Generating intelligent response...")
                if self.send_auto_response(from_email, subject, from_name, email_content):
                    self.mark_as_responded(message_id)

                    # Optional: Mark the original email as read
                    self.imap.store(email_id, '+FLAGS', '\\Seen')

                    print(f"✓ Intelligent response sent successfully to {from_email}")
            
        except Exception as e:
            print(f"Error processing emails: {e}")
        
        finally:
            self.disconnect()
    
    def disconnect(self):
        """Close connections"""
        try:
            self.imap.close()
            self.imap.logout()
            self.smtp.quit()
        except:
            pass
    
    def run_continuously(self, check_interval=300):
        """Run the auto-responder continuously"""
        print(f"Starting auto-responder. Checking every {check_interval} seconds...")
        
        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking for new emails...")
                self.process_emails()
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\nStopping auto-responder...")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")
                time.sleep(60)  # Wait a minute before retrying

# Usage example
if __name__ == "__main__":
    # IMPORTANT: Use an app-specific password, not your regular Yahoo password
    EMAIL = "<EMAIL>"
    PASSWORD = "isreboakabzjwkpu"  # App-specific password from Yahoo
    
    auto_responder = YahooAutoResponder(EMAIL, PASSWORD)
    
    # Run once
    # auto_responder.process_emails()
    
    # Or run continuously (checks every 5 minutes)
    auto_responder.run_continuously(check_interval=300)