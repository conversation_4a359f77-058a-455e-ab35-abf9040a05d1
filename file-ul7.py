import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from datetime import datetime
import time
import sqlite3
import os

class YahooAutoResponder:
    def __init__(self, email_address, password):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.mail.yahoo.com"
        self.smtp_server = "smtp.mail.yahoo.com"
        self.imap_port = 993
        self.smtp_port = 587
        
        # Database to track which emails we've already responded to
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database to track responded emails"""
        self.conn = sqlite3.connect('responded_emails.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS responded_emails (
                message_id TEXT PRIMARY KEY,
                responded_at TIMESTAMP
            )
        ''')
        self.conn.commit()
    
    def connect_imap(self):
        """Connect to Yahoo IMAP server"""
        try:
            self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.imap.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"IMAP connection error: {e}")
            return False
    
    def connect_smtp(self):
        """Connect to Yahoo SMTP server"""
        try:
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()
            self.smtp.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def check_if_responded(self, message_id):
        """Check if we've already responded to this email"""
        self.cursor.execute(
            "SELECT message_id FROM responded_emails WHERE message_id = ?", 
            (message_id,)
        )
        return self.cursor.fetchone() is not None
    
    def mark_as_responded(self, message_id):
        """Mark email as responded in database"""
        self.cursor.execute(
            "INSERT INTO responded_emails (message_id, responded_at) VALUES (?, ?)",
            (message_id, datetime.now())
        )
        self.conn.commit()
    
    def get_unread_emails(self):
        """Fetch unread emails from inbox"""
        try:
            self.imap.select('INBOX')
            status, messages = self.imap.search(None, 'UNSEEN')
            
            if status != 'OK':
                print("Error searching for emails")
                return []
            
            email_ids = messages[0].split()
            emails = []
            
            for email_id in email_ids:
                status, msg_data = self.imap.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    continue
                
                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])
                        emails.append((email_id, msg))
            
            return emails
        
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def create_auto_response(self, original_subject, sender_name):
        """Create auto-response message"""
        # Customize this message as needed
        response_body = f"""Dear {sender_name},

Thank you for your email. This is an automated response to let you know that I have received your message.

I will review your email and get back to you as soon as possible.

If this is urgent, please contact me at [alternative contact].

Best regards,
[Your Name]

---
This is an automated response generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return response_body
    
    def send_auto_response(self, to_email, original_subject, sender_name):
        """Send auto-response email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_address
            msg['To'] = to_email
            msg['Subject'] = f"Re: {original_subject}"
            
            body = self.create_auto_response(original_subject, sender_name)
            msg.attach(MIMEText(body, 'plain'))
            
            self.smtp.send_message(msg)
            print(f"Auto-response sent to {to_email}")
            return True
            
        except Exception as e:
            print(f"Error sending auto-response: {e}")
            return False
    
    def process_emails(self):
        """Main process to check and respond to emails"""
        if not self.connect_imap() or not self.connect_smtp():
            print("Failed to connect to email servers")
            return
        
        try:
            unread_emails = self.get_unread_emails()
            print(f"Found {len(unread_emails)} unread emails")
            
            for email_id, msg in unread_emails:
                # Extract email details
                message_id = msg.get('Message-ID', '')
                from_email = email.utils.parseaddr(msg['From'])[1]
                from_name = email.utils.parseaddr(msg['From'])[0] or from_email.split('@')[0]
                subject = msg['Subject'] or "No Subject"
                
                # Skip if we've already responded
                if self.check_if_responded(message_id):
                    print(f"Already responded to email from {from_email}")
                    continue
                
                # Skip if it's from yourself (avoid loops)
                if from_email.lower() == self.email_address.lower():
                    continue
                
                # Skip if it appears to be an automated email
                if any(keyword in from_email.lower() for keyword in ['noreply', 'no-reply', 'automated']):
                    print(f"Skipping automated email from {from_email}")
                    continue
                
                # Send auto-response
                if self.send_auto_response(from_email, subject, from_name):
                    self.mark_as_responded(message_id)
                    
                    # Optional: Mark the original email as read
                    self.imap.store(email_id, '+FLAGS', '\\Seen')
            
        except Exception as e:
            print(f"Error processing emails: {e}")
        
        finally:
            self.disconnect()
    
    def disconnect(self):
        """Close connections"""
        try:
            self.imap.close()
            self.imap.logout()
            self.smtp.quit()
        except:
            pass
    
    def run_continuously(self, check_interval=300):
        """Run the auto-responder continuously"""
        print(f"Starting auto-responder. Checking every {check_interval} seconds...")
        
        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking for new emails...")
                self.process_emails()
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\nStopping auto-responder...")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")
                time.sleep(60)  # Wait a minute before retrying

# Usage example
if __name__ == "__main__":
    # IMPORTANT: Use an app-specific password, not your regular Yahoo password
    EMAIL = "<EMAIL>"
    PASSWORD = "isreboakabzjwkpu"  # App-specific password from Yahoo
    
    auto_responder = YahooAutoResponder(EMAIL, PASSWORD)
    
    # Run once
    # auto_responder.process_emails()
    
    # Or run continuously (checks every 5 minutes)
    auto_responder.run_continuously(check_interval=300)