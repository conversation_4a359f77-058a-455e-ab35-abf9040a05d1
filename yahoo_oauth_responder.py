import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from datetime import datetime
import time
import sqlite3
import base64

class YahooOAuthResponder:
    def __init__(self, email_address, password):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.mail.yahoo.com"
        self.smtp_server = "smtp.mail.yahoo.com"
        self.imap_port = 993
        self.smtp_port = 587
        
        # Database to track which emails we've already responded to
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database to track responded emails"""
        self.conn = sqlite3.connect('responded_emails.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS responded_emails (
                message_id TEXT PRIMARY KEY,
                responded_at TIMESTAMP
            )
        ''')
        self.conn.commit()
    
    def connect_imap_with_retry(self):
        """Connect to Yahoo IMAP server with multiple retry strategies"""
        strategies = [
            # Strategy 1: Standard SSL connection
            lambda: self._connect_imap_ssl(),
            # Strategy 2: Try with different authentication
            lambda: self._connect_imap_auth(),
        ]
        
        for i, strategy in enumerate(strategies, 1):
            try:
                print(f"Trying connection strategy {i}...")
                if strategy():
                    print(f"✓ Connection successful with strategy {i}")
                    return True
            except Exception as e:
                print(f"✗ Strategy {i} failed: {e}")
                continue
        
        return False
    
    def _connect_imap_ssl(self):
        """Standard SSL IMAP connection"""
        self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
        self.imap.login(self.email_address, self.password)
        return True
    
    def _connect_imap_auth(self):
        """Alternative authentication method"""
        self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
        # Try XOAUTH2 if available
        auth_string = f'user={self.email_address}\x01auth=Bearer {self.password}\x01\x01'
        auth_string = base64.b64encode(auth_string.encode()).decode()
        try:
            self.imap.authenticate('XOAUTH2', lambda x: auth_string)
            return True
        except:
            # Fallback to regular login
            self.imap.login(self.email_address, self.password)
            return True
    
    def connect_smtp_with_retry(self):
        """Connect to Yahoo SMTP server with retry strategies"""
        try:
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()
            self.smtp.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def test_connection(self):
        """Test both IMAP and SMTP connections"""
        print("Testing Yahoo Mail connection...")
        print(f"Email: {self.email_address}")
        
        # Test IMAP
        print("\n1. Testing IMAP connection...")
        if self.connect_imap_with_retry():
            print("✓ IMAP connection successful!")
            try:
                self.imap.logout()
            except:
                pass
        else:
            print("✗ IMAP connection failed!")
            return False
        
        # Test SMTP
        print("\n2. Testing SMTP connection...")
        if self.connect_smtp_with_retry():
            print("✓ SMTP connection successful!")
            try:
                self.smtp.quit()
            except:
                pass
        else:
            print("✗ SMTP connection failed!")
            return False
        
        print("\n✓ All connections successful!")
        return True

# Test the connection
if __name__ == "__main__":
    EMAIL = "<EMAIL>"
    PASSWORD = "Zied74198462342"
    
    responder = YahooOAuthResponder(EMAIL, PASSWORD)
    responder.test_connection()
