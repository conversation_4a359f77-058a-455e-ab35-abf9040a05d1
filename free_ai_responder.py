import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import time
import sqlite3
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
import torch

class FreeAIEmailResponder:
    def __init__(self, email_address, password):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.mail.yahoo.com"
        self.smtp_server = "smtp.mail.yahoo.com"
        self.imap_port = 993
        self.smtp_port = 587
        
        # Initialize AI model (using a free model)
        self.init_ai_model()
        
        # Database to track which emails we've already responded to
        self.init_database()
    
    def init_ai_model(self):
        """Initialize free AI model for text generation"""
        try:
            print("Loading AI model... This may take a few minutes on first run.")
            
            # Use a smaller, free model that works well for email responses
            model_name = "microsoft/DialoGPT-medium"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            
            # Add padding token if it doesn't exist
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            print("✓ AI model loaded successfully!")
            
        except Exception as e:
            print(f"Error loading AI model: {e}")
            print("Falling back to template-based responses")
            self.model = None
            self.tokenizer = None
    
    def init_database(self):
        """Initialize SQLite database to track responded emails"""
        self.conn = sqlite3.connect('responded_emails.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS responded_emails (
                message_id TEXT PRIMARY KEY,
                responded_at TIMESTAMP,
                original_subject TEXT,
                sender_email TEXT,
                ai_response TEXT
            )
        ''')
        self.conn.commit()
    
    def connect_imap(self):
        """Connect to Yahoo IMAP server"""
        try:
            self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.imap.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"IMAP connection error: {e}")
            return False
    
    def connect_smtp(self):
        """Connect to Yahoo SMTP server"""
        try:
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()
            self.smtp.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def extract_email_content(self, msg):
        """Extract text content from email message"""
        content = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            if msg.get_content_type() == "text/plain":
                content = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        return content.strip()
    
    def generate_ai_response(self, sender_name: str, sender_email: str, subject: str, email_content: str) -> str:
        """Generate AI-powered response using free model"""
        try:
            if self.model is None or self.tokenizer is None:
                return self.create_template_response(sender_name, subject, email_content)
            
            # Create a prompt for the AI
            prompt = f"Email from {sender_name} about {subject}: {email_content[:200]}... Response:"
            
            # Tokenize and generate
            inputs = self.tokenizer.encode(prompt, return_tensors='pt', max_length=512, truncation=True)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 100,
                    num_return_sequences=1,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode the response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the generated part
            ai_response = response[len(prompt):].strip()
            
            # Clean up and format the response
            ai_response = self.format_ai_response(ai_response, sender_name)
            
            return ai_response
            
        except Exception as e:
            print(f"AI response generation error: {e}")
            return self.create_template_response(sender_name, subject, email_content)
    
    def format_ai_response(self, ai_text: str, sender_name: str) -> str:
        """Format and clean up AI-generated response"""
        # Clean up the response
        ai_text = ai_text.strip()
        
        # Remove any incomplete sentences
        sentences = ai_text.split('.')
        if len(sentences) > 1 and len(sentences[-1].strip()) < 10:
            ai_text = '.'.join(sentences[:-1]) + '.'
        
        # Create a proper email format
        formatted_response = f"""Dear {sender_name},

Thank you for your email. {ai_text}

I will review your message carefully and get back to you with more details if needed.

Best regards,
[Your Name]

---
This response was generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return formatted_response
    
    def create_template_response(self, sender_name: str, subject: str, email_content: str) -> str:
        """Create template-based response when AI is not available"""
        
        # Simple keyword-based responses
        content_lower = email_content.lower()
        
        if any(word in content_lower for word in ['meeting', 'schedule', 'appointment']):
            specific_response = "I'll check my calendar and get back to you about scheduling."
        elif any(word in content_lower for word in ['question', 'help', 'support']):
            specific_response = "I'll look into your question and provide you with the information you need."
        elif any(word in content_lower for word in ['urgent', 'asap', 'immediately']):
            specific_response = "I understand this is urgent and will prioritize your request."
        else:
            specific_response = "I'll review your message and respond accordingly."
        
        return f"""Dear {sender_name},

Thank you for your email regarding "{subject}".

{specific_response}

I will get back to you as soon as possible.

Best regards,
[Your Name]

---
This is an automated response generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
    
    def check_if_responded(self, message_id):
        """Check if we've already responded to this email"""
        self.cursor.execute(
            "SELECT message_id FROM responded_emails WHERE message_id = ?", 
            (message_id,)
        )
        return self.cursor.fetchone() is not None
    
    def mark_as_responded(self, message_id, subject, sender_email, ai_response):
        """Mark email as responded in database"""
        self.cursor.execute(
            "INSERT INTO responded_emails (message_id, responded_at, original_subject, sender_email, ai_response) VALUES (?, ?, ?, ?, ?)",
            (message_id, datetime.now(), subject, sender_email, ai_response)
        )
        self.conn.commit()
    
    def get_unread_emails(self):
        """Fetch unread emails from inbox"""
        try:
            self.imap.select('INBOX')
            status, messages = self.imap.search(None, 'UNSEEN')
            
            if status != 'OK':
                print("Error searching for emails")
                return []
            
            email_ids = messages[0].split()
            emails = []
            
            for email_id in email_ids:
                status, msg_data = self.imap.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    continue
                
                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])
                        emails.append((email_id, msg))
            
            return emails
        
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def send_ai_response(self, to_email, original_subject, ai_response):
        """Send AI-generated response email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_address
            msg['To'] = to_email
            msg['Subject'] = f"Re: {original_subject}"
            
            msg.attach(MIMEText(ai_response, 'plain'))
            
            self.smtp.send_message(msg)
            print(f"AI response sent to {to_email}")
            return True
            
        except Exception as e:
            print(f"Error sending AI response: {e}")
            return False
    
    def process_emails(self):
        """Main process to check and respond to emails with AI"""
        if not self.connect_imap() or not self.connect_smtp():
            print("Failed to connect to email servers")
            return
        
        try:
            unread_emails = self.get_unread_emails()
            print(f"Found {len(unread_emails)} unread emails")
            
            for email_id, msg in unread_emails:
                # Extract email details
                message_id = msg.get('Message-ID', '')
                from_email = email.utils.parseaddr(msg['From'])[1]
                from_name = email.utils.parseaddr(msg['From'])[0] or from_email.split('@')[0]
                subject = msg['Subject'] or "No Subject"
                email_content = self.extract_email_content(msg)
                
                print(f"\nProcessing email from {from_email}: {subject}")
                
                # Skip if we've already responded
                if self.check_if_responded(message_id):
                    print(f"Already responded to email from {from_email}")
                    continue
                
                # Skip if it's from yourself (avoid loops)
                if from_email.lower() == self.email_address.lower():
                    continue
                
                # Skip if it appears to be an automated email
                if any(keyword in from_email.lower() for keyword in ['noreply', 'no-reply', 'automated']):
                    print(f"Skipping automated email from {from_email}")
                    continue
                
                # Generate AI response
                print("Generating AI response...")
                ai_response = self.generate_ai_response(from_name, from_email, subject, email_content)
                
                # Send AI response
                if self.send_ai_response(from_email, subject, ai_response):
                    self.mark_as_responded(message_id, subject, from_email, ai_response)
                    
                    # Optional: Mark the original email as read
                    self.imap.store(email_id, '+FLAGS', '\\Seen')
                    
                    print(f"✓ AI response sent successfully to {from_email}")
            
        except Exception as e:
            print(f"Error processing emails: {e}")
        
        finally:
            self.disconnect()
    
    def disconnect(self):
        """Close connections"""
        try:
            self.imap.close()
            self.imap.logout()
            self.smtp.quit()
        except:
            pass
    
    def run_continuously(self, check_interval=300):
        """Run the AI auto-responder continuously"""
        print(f"Starting Free AI auto-responder. Checking every {check_interval} seconds...")
        
        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking for new emails...")
                self.process_emails()
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\nStopping AI auto-responder...")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")
                time.sleep(60)  # Wait a minute before retrying

# Usage example
if __name__ == "__main__":
    # Email credentials
    EMAIL = "<EMAIL>"
    PASSWORD = "isreboakabzjwkpu"  # App-specific password
    
    free_ai_responder = FreeAIEmailResponder(EMAIL, PASSWORD)
    
    # Run continuously (checks every 5 minutes)
    free_ai_responder.run_continuously(check_interval=300)
