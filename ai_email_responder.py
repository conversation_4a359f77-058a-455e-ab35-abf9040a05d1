import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from datetime import datetime
import time
import sqlite3
import openai
import os
from typing import Optional

class AIEmailResponder:
    def __init__(self, email_address, password, openai_api_key=None):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.mail.yahoo.com"
        self.smtp_server = "smtp.mail.yahoo.com"
        self.imap_port = 993
        self.smtp_port = 587
        
        # Initialize OpenAI
        if openai_api_key:
            openai.api_key = openai_api_key
        else:
            openai.api_key = os.getenv('OPENAI_API_KEY')
        
        # Database to track which emails we've already responded to
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database to track responded emails"""
        self.conn = sqlite3.connect('responded_emails.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS responded_emails (
                message_id TEXT PRIMARY KEY,
                responded_at TIMESTAMP,
                original_subject TEXT,
                sender_email TEXT,
                ai_response TEXT
            )
        ''')
        self.conn.commit()
    
    def connect_imap(self):
        """Connect to Yahoo IMAP server"""
        try:
            self.imap = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.imap.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"IMAP connection error: {e}")
            return False
    
    def connect_smtp(self):
        """Connect to Yahoo SMTP server"""
        try:
            self.smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
            self.smtp.starttls()
            self.smtp.login(self.email_address, self.password)
            return True
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def extract_email_content(self, msg):
        """Extract text content from email message"""
        content = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            if msg.get_content_type() == "text/plain":
                content = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        return content.strip()
    
    def generate_ai_response(self, sender_name: str, sender_email: str, subject: str, email_content: str) -> str:
        """Generate AI-powered response using OpenAI GPT"""
        try:
            # Create a prompt for the AI
            prompt = f"""
You are an intelligent email auto-responder. Generate a professional, helpful, and personalized response to the following email:

From: {sender_name} ({sender_email})
Subject: {subject}
Content: {email_content}

Guidelines:
1. Be professional and courteous
2. Acknowledge the specific content of their email
3. Provide helpful information if possible
4. If you can't fully address their request, let them know you'll follow up
5. Keep the response concise but warm
6. Sign as the email account owner
7. Don't make promises you can't keep

Generate only the email response content (no subject line):
"""

            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional email assistant that writes thoughtful, personalized responses."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content.strip()
            
            # Add timestamp
            ai_response += f"\n\n---\nThis response was generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            return ai_response
            
        except Exception as e:
            print(f"AI response generation error: {e}")
            # Fallback to simple response
            return self.create_fallback_response(sender_name)
    
    def create_fallback_response(self, sender_name: str) -> str:
        """Fallback response when AI is not available"""
        return f"""Dear {sender_name},

Thank you for your email. I have received your message and will review it carefully.

I will get back to you as soon as possible with a detailed response.

If this is urgent, please feel free to follow up.

Best regards,
[Your Name]

---
This is an automated response generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    def check_if_responded(self, message_id):
        """Check if we've already responded to this email"""
        self.cursor.execute(
            "SELECT message_id FROM responded_emails WHERE message_id = ?", 
            (message_id,)
        )
        return self.cursor.fetchone() is not None
    
    def mark_as_responded(self, message_id, subject, sender_email, ai_response):
        """Mark email as responded in database"""
        self.cursor.execute(
            "INSERT INTO responded_emails (message_id, responded_at, original_subject, sender_email, ai_response) VALUES (?, ?, ?, ?, ?)",
            (message_id, datetime.now(), subject, sender_email, ai_response)
        )
        self.conn.commit()
    
    def get_unread_emails(self):
        """Fetch unread emails from inbox"""
        try:
            self.imap.select('INBOX')
            status, messages = self.imap.search(None, 'UNSEEN')
            
            if status != 'OK':
                print("Error searching for emails")
                return []
            
            email_ids = messages[0].split()
            emails = []
            
            for email_id in email_ids:
                status, msg_data = self.imap.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    continue
                
                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])
                        emails.append((email_id, msg))
            
            return emails
        
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def send_ai_response(self, to_email, original_subject, ai_response):
        """Send AI-generated response email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_address
            msg['To'] = to_email
            msg['Subject'] = f"Re: {original_subject}"
            
            msg.attach(MIMEText(ai_response, 'plain'))
            
            self.smtp.send_message(msg)
            print(f"AI response sent to {to_email}")
            return True
            
        except Exception as e:
            print(f"Error sending AI response: {e}")
            return False
    
    def process_emails(self):
        """Main process to check and respond to emails with AI"""
        if not self.connect_imap() or not self.connect_smtp():
            print("Failed to connect to email servers")
            return
        
        try:
            unread_emails = self.get_unread_emails()
            print(f"Found {len(unread_emails)} unread emails")
            
            for email_id, msg in unread_emails:
                # Extract email details
                message_id = msg.get('Message-ID', '')
                from_email = email.utils.parseaddr(msg['From'])[1]
                from_name = email.utils.parseaddr(msg['From'])[0] or from_email.split('@')[0]
                subject = msg['Subject'] or "No Subject"
                email_content = self.extract_email_content(msg)
                
                print(f"\nProcessing email from {from_email}: {subject}")
                
                # Skip if we've already responded
                if self.check_if_responded(message_id):
                    print(f"Already responded to email from {from_email}")
                    continue
                
                # Skip if it's from yourself (avoid loops)
                if from_email.lower() == self.email_address.lower():
                    continue
                
                # Skip if it appears to be an automated email
                if any(keyword in from_email.lower() for keyword in ['noreply', 'no-reply', 'automated']):
                    print(f"Skipping automated email from {from_email}")
                    continue
                
                # Generate AI response
                print("Generating AI response...")
                ai_response = self.generate_ai_response(from_name, from_email, subject, email_content)
                
                # Send AI response
                if self.send_ai_response(from_email, subject, ai_response):
                    self.mark_as_responded(message_id, subject, from_email, ai_response)
                    
                    # Optional: Mark the original email as read
                    self.imap.store(email_id, '+FLAGS', '\\Seen')
                    
                    print(f"✓ AI response sent successfully to {from_email}")
            
        except Exception as e:
            print(f"Error processing emails: {e}")
        
        finally:
            self.disconnect()
    
    def disconnect(self):
        """Close connections"""
        try:
            self.imap.close()
            self.imap.logout()
            self.smtp.quit()
        except:
            pass
    
    def run_continuously(self, check_interval=300):
        """Run the AI auto-responder continuously"""
        print(f"Starting AI auto-responder. Checking every {check_interval} seconds...")
        
        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking for new emails...")
                self.process_emails()
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\nStopping AI auto-responder...")
                break
            except Exception as e:
                print(f"Unexpected error: {e}")
                time.sleep(60)  # Wait a minute before retrying

# Usage example
if __name__ == "__main__":
    # Email credentials
    EMAIL = "<EMAIL>"
    PASSWORD = "isreboakabzjwkpu"  # App-specific password
    
    # OpenAI API key (get from https://platform.openai.com/api-keys)
    OPENAI_API_KEY = "your-openai-api-key-here"  # Replace with your actual API key
    
    ai_responder = AIEmailResponder(EMAIL, PASSWORD, OPENAI_API_KEY)
    
    # Run continuously (checks every 5 minutes)
    ai_responder.run_continuously(check_interval=300)
